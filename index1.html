<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Star Advisors Top9</title>
	<style type="text/css">
		html,body{
			padding: 0;
			margin: 0;
		}
		.container {
			background-image: url(https://www.allstarmet.com/activity/rank/images/head-bg.png);
			background-size: 100% 100%;
			background-repeat: no-repeat;
			max-width: 500px;
			height: 1960px;
			margin: 0 auto;
			background-color: #12021c;
			position: relative;
		}
		.main {
			position: absolute;
		    top: 21%;
		    width: 100%;
		    background-image: url(https://www.allstarmet.com/activity/rank/images/content-bg.png);
		    background-repeat: no-repeat;
		    background-size: 100% 100%;
		    height: 1500px;
		}
		.rank-date {
		    position: absolute;
		    top: 15.8%;
		    left: 50%;
		    transform: translate(-50%, -50%);
		    color: #FFF;
			text-shadow: 1px 1px 0px #372813, 0px 0px 30px #FFD724;
			font-family: "Source Sans Pro";
			font-size: 16px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
			letter-spacing: 1px;
		}
		
		.rank{
			margin-top: 50px;
		}
		.rank-item {
		    display: flex;
		    height: 120px;
		    padding: 18px 0;
		}
		.rank-btn-l {
			width: 120px;
		    height: 32px;
		    background-image: url(https://www.allstarmet.com/activity/rank/images/btn.png);
		    background-repeat: no-repeat;
		    background-size: 100% 100%;
		    border: none;
		    border-radius: 22px;
		    float: right;
		    margin: 10px 36px 0 0;
		}
		.rank-btn-r {
			width: 120px;
		    height: 32px;
		    background-image: url(https://www.allstarmet.com/activity/rank/images/btn.png);
		    background-repeat: no-repeat;
		    background-size: 100% 100%;
		    border: none;
		    border-radius: 22px;
		    margin: 10px 26px 0 0;
		    float: left;
		}
		.rank-avatar-c-l{
			background-image: url(https://www.allstarmet.com/activity/rank/images/avatar-bg.png);
    		background-repeat: no-repeat;
    		background-size: 100% 100%;
    		width:115px;
    		height:115px;
    		position: relative;
		}
		.rank-avatar-c-r{
			background-image: url(https://www.allstarmet.com/activity/rank/images/avatar-bg.png);
    		background-repeat: no-repeat;
    		background-size: 100% 100%;
    		width:115px;
    		height:115px;
    		margin-right: 40px;
    		position: relative;
		}
		.rank-avatar-l{
			width: 100px;
		    height: 100px;
		    object-fit: cover;
		    margin: 10px 0 0 10px;
		}
		.rank-name{
			font-weight: 700;
			font-size: 18px;
			line-height: 100%;
			vertical-align: middle;
			color: #FFFFFF;
			margin-top: 10px;
		}
		.helped{
			font-family: Source Sans Pro;
			font-weight: 400;
			font-size: 13px;
			line-height: 100%;
			letter-spacing: 0%;
			vertical-align: middle;
			color: #b5b7cd;
		}
		.badge-l{
			position: absolute;
		    top: -8px;
		    left: -8px;
		}
		.badge-r{
			position: absolute;
		    top: -8px;
		    right: -12px;
		}
		.unsubscribe{
			text-align: center;
    		position: absolute;
    		bottom: 0;
		    left: 42%;
		    padding-bottom: 20px;
		}
		.unsubscribe-a{
			color: #c7c3c3;
		    text-decoration: none;
		    font-size: 13px;
		}
	</style>
</head>
<body>
	<div class="container">
		<div>
			<span class="rank-date" id="rank-date"></span>
		</div>
		<div class="main">
			<ul class="rank" id="rank-list"></ul>
		</div>
		<div class="unsubscribe">
			<a href="unsubscribe_link" class="unsubscribe-a">unsubscribe</a>
		</div>
	</div>
	
</body>
</html>

<script type="text/javascript" src="https://www.allstarmet.com/activity/rank/js/jquery-3.2.1.min.js"></script>
<script type="text/javascript">
	const host = "https://api.allstarmet.com";

	initData();

    function initData() {
		$.ajax({
		    type: "get",
		    url: host + '/web/augur/weekly/rank?appId=1', 
		    async: true,
		    dataType: "json",
		    success:function(res){ 
		    	let date = res.data.date;
		    	$("#rank-date").html(date);

		       	let rankList = res.data.rank;
				let content = "";
				rankList.forEach(function(item, index, array){
				    if (index == 0) {
				    	content += '<li class="rank-item"><div style="flex: 3"><div class="rank-avatar-c-l"><img class="badge-l" src="https://www.allstarmet.com/activity/rank/images/badge1.png"><img class="rank-avatar-l" src="'+item.avatar+'" /></div></div><div style="flex:7;margin-left: 30px;"><p class="rank-name">'+item.username+'</p><p class="helped" style="text-align: left!important;">Helped '+item.serviceUsers+'+ Peoples</p><div class="rank-btn" style="flex:30%;display: contents;"><a href="https://play.google.com/store/apps/details?id=com.starmet&hl=en" target="_black" class="rank-btn-l"></a></div></div></li>';
				    } else if (index == 1) {
				    	content += '<li class="rank-item"><div style="flex:7;"><p class="rank-name">'+item.username+'</p><p class="helped" style="text-align: left!important;">Helped '+item.serviceUsers+'+ Peoples</p><div class="rank-btn" style="flex:30%;display: contents;"><a href="https://play.google.com/store/apps/details?id=com.starmet&hl=en" target="_black" class="rank-btn-r"></a></div></div><div style="flex: 3"><div class="rank-avatar-c-r"><img class="badge-r" src="https://www.allstarmet.com/activity/rank/images/badge2.png"><img class="rank-avatar-l" src="'+item.avatar+'" /></div></div></li>';
				    } else if (index == 2) {
				    	content += '<li class="rank-item"><div style="flex: 3"><div class="rank-avatar-c-l"><img class="badge-l" src="https://www.allstarmet.com/activity/rank/images/badge3.png"><img class="rank-avatar-l" src="'+item.avatar+'" /></div></div><div style="flex:7;margin-left: 30px;"><p class="rank-name">'+item.username+'</p><p class="helped" style="text-align: left!important;">Helped '+item.serviceUsers+'+ Peoples</p><div class="rank-btn" style="flex:30%;display: contents;"><a href="https://play.google.com/store/apps/details?id=com.starmet&hl=en" target="_black" class="rank-btn-l"></a></div></div></li>';
				    } else {
				    	if (index % 2 == 1) {
				    		content += '<li class="rank-item"><div style="flex:7;"><p class="rank-name">'+item.username+'</p><p class="helped" style="text-align: left!important;">Helped '+item.serviceUsers+'+ Peoples</p><div class="rank-btn" style="flex:30%;display: contents;"><a href="https://play.google.com/store/apps/details?id=com.starmet&hl=en" target="_black" class="rank-btn-r"></a></div></div><div style="flex: 3"><div class="rank-avatar-c-r"><img class="rank-avatar-l" src="'+item.avatar+'" /></div></div></li>';
				    	} else {
				    		content += '<li class="rank-item"><div style="flex: 3"><div class="rank-avatar-c-l"><img class="rank-avatar-l" src="'+item.avatar+'" /></div></div><div style="flex:7;margin-left: 30px;"><p class="rank-name">'+item.username+'</p><p class="helped" style="text-align: left!important;">Helped '+item.serviceUsers+'+ Peoples</p><div class="rank-btn" style="flex:30%;display: contents;"><a href="https://play.google.com/store/apps/details?id=com.starmet&hl=en" target="_black" class="rank-btn-l"></a></div></div></li>';
				    	}
				    }
				 });
				$("#rank-list").html(content);
		    }
		})
 
    }
    $(".unsubscribe-a").click(function(e){
    	e.preventDefault(); // 阻止页面跳转
    	let url = this.href;
    	$.ajax({
		    type: "get",
		    url: url, 
		    async: true,
		    success:function(res){
		    	alert("The unsubscription is successful");
		    }
		})
    });
</script>