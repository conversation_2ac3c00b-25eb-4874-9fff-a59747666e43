<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Star Advisors Top9</title>
		<style type="text/css">
			html,
			body {
				padding: 0;
				margin: 0;
			}
			.container {
				background-image: url(https://www.allstarmet.com/activity/rank/images/head-bg.png);
				background-size: 100% 100%;
				background-repeat: no-repeat;
				max-width: 500px;
				height: 2000px;
				font-size: 16px;
				margin: 0 auto;
				background-color: #12021c;
			}
			.main {
				margin-top: 6em;
				width: 100%;
				font-size: 16px;
				background-image: url(https://www.allstarmet.com/activity/rank/images/content-bg.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				height: 1236px;
			}
			.rank-date-box {
				padding-top: 19em;
				text-align: center;
			}
			.rank-date {
				color: #fff;
				text-shadow: 1px 1px 0px #372813, 0px 0px 30px #ffd724;
				font-family: 'Source Sans Pro';
				font-size: 16px;
				font-style: normal;
				font-weight: 600;
				line-height: normal;
				letter-spacing: 1px;
			}

			.rank {
				margin-top: 50px;
			}
			.rank-item {
				display: flex;
				height: 117px;
				padding-top: 1em;
			}
			.rank-btn-l {
				width: 120px;
				height: 32px;
				background-image: url(https://www.allstarmet.com/activity/rank/images/btn.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				border: none;
				border-radius: 22px;
				float: right;
				margin: 10px 36px 0 10em;
			}
			.rank-btn-r {
				width: 120px;
				height: 32px;
				background-image: url(https://www.allstarmet.com/activity/rank/images/btn.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				border: none;
				border-radius: 22px;
				margin: 10px 26px 0 0;
				float: left;
			}
			.rank-avatar-c-l {
				background-image: url(https://www.allstarmet.com/activity/rank/images/avatar-bg.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				width: 115px;
				height: 115px;
				display: inline-block;
			}
			.rank-avatar-c-r {
				background-image: url(https://www.allstarmet.com/activity/rank/images/avatar-bg.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				width: 115px;
				height: 115px;
				margin-left: 10em;
				display: inline-block;
			}
			.rank-avatar-l {
				width: 100px;
				height: 100px;
				object-fit: cover;
				margin: 10px 0 0 10px;
			}
			.rank-name {
				font-weight: 700;
				font-size: 18px;
				line-height: 100%;
				vertical-align: middle;
				color: #ffffff;
				margin-top: 10px;
			}
			.helped {
				font-family: Source Sans Pro;
				font-weight: 400;
				font-size: 13px;
				line-height: 100%;
				letter-spacing: 0%;
				vertical-align: middle;
				color: #b5b7cd;
			}
			.badge-l {
				display: table;
				margin: -10px 0 0 -6px;
			}
			.badge-r {
				display: inline-block;
				margin-top: -30px;
				margin-left: 85px;
			}
			.unsubscribe {
				text-align: center;
				padding-bottom: 20px;
				margin-top: 50px;
			}
			.unsubscribe-a {
				color: #c7c3c3;
				text-decoration: none;
				font-size: 13px;
			}
		</style>
	</head>
	<body>
		<div class="container">
			<div class="rank-date-box">
				<span class="rank-date" id="rank-date">  </span>
			</div>
			<div class="main">
				<ul class="rank" id="rank-list">
					<!-- <li class="rank-item">
						<div style="">
							<div class="rank-avatar-c-l">
								<img class="rank-avatar-l" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500112346/media/20250212/621db146bb416b1f54c377bfa894225f.jpg_540x0.jpg" />
							</div>
						</div>
						<div style="margin-left: 30px">
							<p class="rank-name">
								<img src="https://www.allstarmet.com/activity/rank/images/badge1.png" alt="" style="width: 20px; height: 22px; vertical-align: middle" />
								<span>Psychic Bianca</span>
							</p>
							<p class="helped" style="text-align: left !important">Helped 144+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-l"></a></div>
						</div>
					</li>
					<li class="rank-item">
						<div>
							<p class="rank-name">
								<img src="https://www.allstarmet.com/activity/rank/images/badge2.png" alt="" style="width: 20px; height: 22px; vertical-align: middle" />
								<span>Psychic Bianca</span>
							</p>
							<p class="helped" style="text-align: left !important">Helped 144+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-r"></a></div>
						</div>
						<div>
							<div class="rank-avatar-c-r">
								<img class="rank-avatar-l" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500112346/media/20250212/621db146bb416b1f54c377bfa894225f.jpg_540x0.jpg" />
							</div>
						</div>
					</li>
					<li class="rank-item">
						<div style="">
							<div class="rank-avatar-c-l">
								<img class="rank-avatar-l" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500112346/media/20250212/621db146bb416b1f54c377bfa894225f.jpg_540x0.jpg" />
							</div>
						</div>
						<div style="margin-left: 30px">
							<p class="rank-name">
								<img src="https://www.allstarmet.com/activity/rank/images/badge1.png" alt="" style="width: 20px; height: 22px; vertical-align: middle" />
								<span>Psychic Bianca</span>
							</p>
							<p class="helped" style="text-align: left !important">Helped 144+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-l"></a></div>
						</div>
					</li> -->
					<!-- <li class="rank-item">
						<div style="flex: 7">
							<p class="rank-name">Spiritual Lily</p>
							<p class="helped" style="text-align: left !important">Helped 35+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-r"></a></div>
						</div>
						<div style="flex: 3">
							<div class="rank-avatar-c-r"><img class="rank-avatar-l" src="http://cdn.allstarmet.com/thumbnail/starmet/pro/500111855/media/23a77a66f9145f217da5235eca1cba94.jpg_540x0.jpg" /></div>
						</div>
					</li>
					<li class="rank-item">
						<div style="flex: 3">
							<div class="rank-avatar-c-l"><img class="rank-avatar-l" src="https://cdn-3.staronlykol.com/thumbnail/starmet/pro/500110487/media/9cb7657cc0b209918ea191dbe7a5a993.jpg_540x0.jpg" /></div>
						</div>
						<div style="flex: 7; margin-left: 30px">
							<p class="rank-name">Psychic Sally</p>
							<p class="helped" style="text-align: left !important">Helped 123+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-l"></a></div>
						</div>
					</li> -->
				</ul>
			</div>
			<div class="unsubscribe">
				<a href="unsubscribe_link" class="unsubscribe-a">unsubscribe</a>
			</div>
		</div>
	</body>
</html>

<script type="text/javascript" src="https://www.healtalkapp.com/activity/rank/js/jquery-3.2.1.min.js"></script>
<script type="text/javascript">
	const host = 'https://api.allstarmet.com'

	initData()

	function initData() {
		$.ajax({
			type: 'get',
			url: host + '/web/augur/weekly/rank?appId=1',
			async: true,
			dataType: 'json',
			success: function (res) {
				let date = res.data.date
				$('#rank-date').html(date)

				let rankList = res.data.rank
				let content = ''
				rankList.forEach(function (item, index, array) {
					if (index == 0) {
						content += `<li class="rank-item">
                        <div style="">
							<div class="rank-avatar-c-l">
                                <img class="rank-avatar-l" src="${item.avatar}" />
							</div>
						</div>
						<div style=" margin-left: 30px">
							<p class="rank-name">
                                <img src="https://www.allstarmet.com/activity/rank/images/badge1.png" alt="" style="width:20px;height:22px;vertical-align: middle;">
                                <span>${item.username}</span>
                            </p>
							<p class="helped" style="text-align: left !important">Helped ${item.serviceUsers}+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-l"></a></div>
						</div></li>`
					} else if (index == 1) {
						content += `<li class="rank-item">
						<div style="">
							<p class="rank-name">
                                <img src="https://www.allstarmet.com/activity/rank/images/badge2.png" alt="" style="width:20px;height:22px;vertical-align: middle;">
                                <span>${item.username}</span>
                            </p>
							<p class="helped" style="text-align: left !important">Helped ${item.serviceUsers}+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-r"></a></div>
						</div>
                        <div style="">
							<div class="rank-avatar-c-r">
                                <img class="rank-avatar-l" src="${item.avatar}" />
							</div>
						</div>
                        </li>`
					} else if (index == 2) {
						content += `
                        <li class="rank-item">
                        <div style="">
							<div class="rank-avatar-c-l">
                                <img class="rank-avatar-l" src="${item.avatar}" />
							</div>
						</div>
						<div style=" margin-left: 30px">
							<p class="rank-name">
                                <img src="https://www.allstarmet.com/activity/rank/images/badge3.png" alt="" style="width:20px;height:22px;vertical-align: middle;">
                                <span>${item.username}</span>
                            </p>
							<p class="helped" style="text-align: left !important">Helped ${item.serviceUsers}+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-l"></a></div>
						</div></li>
                        `
					} else {
						if (index % 2 == 1) {
							content += `<li class="rank-item">
						<div style="">
							<p class="rank-name">
                                <span>${item.username}</span>
                            </p>
							<p class="helped" style="text-align: left !important">Helped ${item.serviceUsers}+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-r"></a></div>
						</div>
                        <div style="">
							<div class="rank-avatar-c-r">
                                <img class="rank-avatar-l" src="${item.avatar}" />
							</div>
						</div>
                        </li>`
						} else {
							content += `<li class="rank-item">
                        <div style="">
							<div class="rank-avatar-c-l">
                                <img class="rank-avatar-l" src="${item.avatar}" />
							</div>
						</div>
						<div style=" margin-left: 30px">
							<p class="rank-name">
                               
                                <span>${item.username}</span>
                            </p>
							<p class="helped" style="text-align: left !important">Helped ${item.serviceUsers}+ Peoples</p>
							<div class="rank-btn" style="flex: 30%; display: contents"><a href="https://play.google.com/store/apps/details?id=com.starmet&amp;hl=en" target="_black" class="rank-btn-l"></a></div>
						</div></li>`
						}
					}
				})
				$('#rank-list').html(content)
			},
		})
        $(".unsubscribe-a").click(function(e){
    	e.preventDefault(); // 阻止页面跳转
    	let url = this.href;
    	$.ajax({
		    type: "get",
		    url: url, 
		    async: true,
		    success:function(res){
		    	alert("The unsubscription is successful");
		    }
		})
    });
	}
</script>
